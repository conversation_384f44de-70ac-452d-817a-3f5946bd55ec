import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/screens/widgets/catalog_section.dart';

class CatalogViewall extends StatelessWidget {
  const CatalogViewall({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Map<String, String>> catalogList = [
      {
        'image': 'assets/images/catalogs/cat1.jpg',
        'brand': 'Brand One',
        'catalog': 'CAT-101',
      },
      {
        'image': 'assets/images/catalogs/cat2.jpg',
        'brand': 'Brand Two',
        'catalog': 'CAT-102',
      },
      {
        'image': 'assets/images/catalogs/cat3.jpg',
        'brand': 'Brand Three',
        'catalog': 'CAT-103',
      },
      {
        'image': 'assets/images/catalogs/cat4.jpg',
        'brand': 'Brand Four',
        'catalog': 'CAT-104',
      },
      {
        'image': 'assets/images/catalogs/cat5.jpg',
        'brand': 'Brand Five',
        'catalog': 'CAT-105',
      },
      {
        'image': 'assets/images/catalogs/cat6.jpg',
        'brand': 'Brand Six',
        'catalog': 'CAT-106',
      },
      {
        'image': 'assets/images/catalogs/cat7.jpg',
        'brand': 'Brand Seven',
        'catalog': 'CAT-107',
      },
    ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Catalogs'),
        foregroundColor: Colors.white,
        backgroundColor: const Color(0xFF005368),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(14),
        child: CatalogSection(catalogList: catalogList,maxItems: null,),
      )
    );
  }
}
