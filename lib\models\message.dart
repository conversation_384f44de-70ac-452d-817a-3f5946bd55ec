import 'package:cloud_firestore/cloud_firestore.dart';

enum MessageType {
  text,
  image,
  file,
  audio,
  video,
  location,
  contact,
  order,
  catalog,
}

enum MessageStatus { sending, sent, delivered, read, failed }

class Message {
  final String id;
  final String senderId;
  final String senderName;
  final String? senderProfileUrl;
  final MessageType type;
  final String? text;
  final String? mediaUrl;
  final String? fileName;
  final int? fileSize;
  final String? thumbnailUrl;
  final MessageStatus status;
  final DateTime timestamp;
  final String? replyToMessageId;
  final String? replyToText;
  final String? replyToSenderName;
  final bool isForwarded;
  final List<String> readBy;
  final Map<String, dynamic>? metadata; // For order, catalog, location data

  Message({
    required this.id,
    required this.senderId,
    required this.senderName,
    this.senderProfileUrl,
    required this.type,
    this.text,
    this.mediaUrl,
    this.fileName,
    this.fileSize,
    this.thumbnailUrl,
    this.status = MessageStatus.sending,
    required this.timestamp,
    this.replyToMessageId,
    this.replyToText,
    this.replyToSenderName,
    this.isForwarded = false,
    this.readBy = const [],
    this.metadata,
  });

  // Convert Message to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'senderId': senderId,
      'senderName': senderName,
      'senderProfileUrl': senderProfileUrl,
      'type': type.name,
      'text': text,
      'mediaUrl': mediaUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'thumbnailUrl': thumbnailUrl,
      'status': status.name,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'replyToMessageId': replyToMessageId,
      'replyToText': replyToText,
      'replyToSenderName': replyToSenderName,
      'isForwarded': isForwarded,
      'readBy': readBy,
      'metadata': metadata,
    };
  }

  // Create Message from Firestore document
  factory Message.fromMap(Map<String, dynamic> map) {
    return Message(
      id: map['id'] ?? '',
      senderId: map['senderId'] ?? '',
      senderName: map['senderName'] ?? '',
      senderProfileUrl: map['senderProfileUrl'],
      type: MessageType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => MessageType.text,
      ),
      text: map['text'],
      mediaUrl: map['mediaUrl'],
      fileName: map['fileName'],
      fileSize: map['fileSize'],
      thumbnailUrl: map['thumbnailUrl'],
      status: MessageStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => MessageStatus.sent,
      ),
      timestamp: _parseDateTime(map['timestamp']),
      replyToMessageId: map['replyToMessageId'],
      replyToText: map['replyToText'],
      replyToSenderName: map['replyToSenderName'],
      isForwarded: map['isForwarded'] ?? false,
      readBy: List<String>.from(map['readBy'] ?? []),
      metadata: map['metadata'],
    );
  }

  // Helper method to parse DateTime from various formats
  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now();
    }

    if (value is int) {
      return DateTime.fromMillisecondsSinceEpoch(value);
    }

    if (value is Timestamp) {
      return value.toDate();
    }

    if (value is DateTime) {
      return value;
    }

    // Fallback to current time if we can't parse
    return DateTime.now();
  }

  // Create Message from Firestore DocumentSnapshot
  factory Message.fromDocument(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Message.fromMap(data);
  }

  // Copy with method for updating message data
  Message copyWith({
    String? id,
    String? senderId,
    String? senderName,
    String? senderProfileUrl,
    MessageType? type,
    String? text,
    String? mediaUrl,
    String? fileName,
    int? fileSize,
    String? thumbnailUrl,
    MessageStatus? status,
    DateTime? timestamp,
    String? replyToMessageId,
    String? replyToText,
    String? replyToSenderName,
    bool? isForwarded,
    List<String>? readBy,
    Map<String, dynamic>? metadata,
  }) {
    return Message(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      senderProfileUrl: senderProfileUrl ?? this.senderProfileUrl,
      type: type ?? this.type,
      text: text ?? this.text,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      replyToText: replyToText ?? this.replyToText,
      replyToSenderName: replyToSenderName ?? this.replyToSenderName,
      isForwarded: isForwarded ?? this.isForwarded,
      readBy: readBy ?? this.readBy,
      metadata: metadata ?? this.metadata,
    );
  }

  // Check if message is sent by current user
  bool isSentByMe(String currentUserId) {
    return senderId == currentUserId;
  }

  // Check if message is read by specific user
  bool isReadBy(String userId) {
    return readBy.contains(userId);
  }

  @override
  String toString() {
    return 'Message(id: $id, senderId: $senderId, type: $type, text: $text, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Message && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
