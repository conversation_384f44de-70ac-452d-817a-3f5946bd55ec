import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/screens/widgets/brands_section.dart';

class BrandsViewallScreen extends StatelessWidget {
  const BrandsViewallScreen({super.key});

  @override
  Widget build(BuildContext context) {
      final List<String> brandImages = [
    'assets/images/brands/brand1.jpg',
    'assets/images/brands/brand2.jpg',
    'assets/images/brands/brand3.jpg',
    'assets/images/brands/brand4.jpg',
    'assets/images/brands/brand5.jpg',
    'assets/images/brands/brand6.jpg',
    'assets/images/brands/brand7.jpg',
  ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Brands'),
        foregroundColor: Colors.white,
        backgroundColor: const Color(0xFF005368),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(14),
        child: BrandsSection(
          brandImages: brandImages,
          maxItems: null,   // This shows all brands!
        ),
      ),
    );
  }
}