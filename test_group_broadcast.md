# Group Broadcast Functionality Test Plan

## Overview
The group broadcast functionality allows admin users to create groups and send messages that appear as individual messages to each group member, rather than traditional group messages.

## Key Features Implemented

### 1. Tab Label Change
- ✅ Changed "Groups" tab to "Group" in chat main page

### 2. Group Creation Restrictions
- ✅ Only admin users can create groups
- ✅ Non-admin users see "Only admin can create groups" message
- ✅ FloatingActionButton for group creation only visible to admin

### 3. Broadcast Message Logic
- ✅ When admin sends message in group chat, it triggers `_sendBroadcastMessage`
- ✅ Creates individual chats with each group member
- ✅ Sends message to each individual chat separately
- ✅ Also saves message in group chat for admin reference
- ✅ Updates unread counts for recipients

### 4. Group Chat UI for Admin
- ✅ Shows "Broadcast to X members" subtitle in app bar
- ✅ Displays broadcast indicator above message input
- ✅ Uses radio icon to indicate broadcasting

### 5. Individual Chat Display for Recipients
- ✅ Recipients receive messages in their individual chat with admin
- ✅ Messages appear as regular individual messages, not group messages
- ✅ Individual chat streams filter correctly for ChatType.individual

## Test Scenarios

### Scenario 1: Admin Creates Group
1. Login as admin user
2. Navigate to Group tab
3. Verify "Create Group" button is visible
4. Create a new group with multiple members
5. Verify group appears in groups list

### Scenario 2: Non-Admin User Views Groups
1. Login as non-admin user (manufacturer/retailer/distributor)
2. Navigate to Group tab
3. Verify "Create Group" button is NOT visible
4. Verify "Only admin can create groups" message appears if no groups exist

### Scenario 3: Admin Sends Broadcast Message
1. Login as admin user
2. Open a group chat
3. Verify "Broadcast to X members" subtitle appears
4. Verify broadcast indicator appears above message input
5. Send a text message
6. Verify message appears in group chat for admin
7. Verify individual chats are created with each member

### Scenario 4: Recipients Receive Individual Messages
1. Login as group member (non-admin)
2. Navigate to Chat tab (individual chats)
3. Verify new individual chat with admin appears
4. Open individual chat with admin
5. Verify broadcast message appears as regular individual message
6. Verify no group chat indicators

### Scenario 5: Image Broadcast
1. Login as admin user
2. Open group chat
3. Send an image message
4. Verify image is broadcast to all members as individual messages
5. Verify recipients receive image in individual chat with admin

## Expected Behavior

### For Admin Users:
- Can create groups
- Can see all groups they created or are members of
- When sending messages in group chat:
  - See broadcast indicator
  - Message appears in group chat
  - Message is sent to each member individually

### For Non-Admin Users:
- Cannot create groups
- Can see groups they are members of (if any)
- Receive admin's group messages as individual messages
- No indication that message came from group broadcast

## Technical Implementation Details

### Database Structure:
- Groups maintain normal structure in Firestore
- Broadcast individual chats created with special flags:
  - `isBroadcastChat: true`
  - `broadcastSenderId: adminUserId`
- Individual chats use chatId format: `{adminId}_{memberId}` or `{memberId}_{adminId}`
- Messages stored in individual chat collections
- Group chat also stores copy for admin reference

### Message Flow:
1. Admin sends message in group chat
2. `sendTextMessage` detects group chat + admin role
3. Calls `_sendBroadcastMessage` instead of normal message flow
4. For each group member:
   - Creates/gets broadcast individual chat using `_createBroadcastIndividualChat`
   - Sends message to individual chat
   - Updates unread count
5. Also saves message in group chat for admin

### Chat Filtering:
- Admin users: See all chats EXCEPT broadcast individual chats they created
- Non-admin users: See broadcast individual chats normally (as regular individual chats)
- `getUserChatsStream` filters out broadcast chats for admin based on `isBroadcastChat` and `broadcastSenderId` fields

### Fixed Issues:
- ✅ Admin no longer sees duplicate individual chats in their chat list
- ✅ Admin only sees group chats for broadcast management
- ✅ Recipients see individual chats with admin (no group chat confusion)
- ✅ Broadcast messages appear as personal messages to recipients

This implementation ensures that:
- Admin can manage group communications efficiently without chat list clutter
- Recipients receive messages as personal communications
- No confusion about group vs individual messages for recipients
- Admin maintains visibility of broadcast activity through group chat
- No duplicate chats in admin's individual chat list
