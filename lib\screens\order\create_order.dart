import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class CreateOrderScreen extends StatefulWidget {
  const CreateOrderScreen({super.key});

  @override
  State<CreateOrderScreen> createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends State<CreateOrderScreen> {
  String? selectedCustomer;
  DateTime selectedDate = DateTime.now();
  String serialNumber = '';
  String? selectedCatalog;
  int quantity = 0;
  double amount = 0.0;
  double gst = 0.0;
  double discount = 0.0;
  double get total => quantity * amount;
  double get grandTotal => total + gst - discount;

  final List<String> customers = ['XYZM Garments', 'ZZZH Garments', 'ABCD Fashions'];
  final List<String> catalogs = ['CAT12345', 'CAT45678', 'CAT9990'];

  Future<void> _selectDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (pickedDate != null) {
      setState(() {
        selectedDate = pickedDate;
      });
    }
  }

  Widget _buildTextField({
    required String label,
    String? initialValue,
    void Function(String)? onChanged,
    TextInputType inputType = TextInputType.text,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 6),
        TextFormField(
          initialValue: initialValue,
          keyboardType: inputType,
          onChanged: onChanged,
          decoration: InputDecoration(
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: GoogleFonts.poppins(fontWeight: FontWeight.w600)),
        Text(value, style: GoogleFonts.poppins()),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "New Order",
          style: GoogleFonts.poppins(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: const Color(0xFF005368),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Select Customer
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "Select Customer",
                style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(height: 6),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              ),
              value: selectedCustomer,
              items: customers
                  .map((customer) => DropdownMenuItem(
                        value: customer,
                        child: Text(customer),
                      ))
                  .toList(),
              onChanged: (value) => setState(() => selectedCustomer = value),
            ),
            const SizedBox(height: 16),

            // Select Date
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "Select Date",
                style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(height: 6),
            GestureDetector(
              onTap: _selectDate,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(DateFormat('dd/MM/yyyy').format(selectedDate)),
              ),
            ),
            const SizedBox(height: 16),

            // Serial Number
            _buildTextField(
              label: "Serial Number",
              onChanged: (val) => setState(() => serialNumber = val),
            ),

            // Select Catalog
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                "Select Catalog",
                style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(height: 6),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              ),
              value: selectedCatalog,
              items: catalogs
                  .map((catalog) => DropdownMenuItem(
                        value: catalog,
                        child: Text(catalog),
                      ))
                  .toList(),
              onChanged: (value) => setState(() => selectedCatalog = value),
            ),
            const SizedBox(height: 16),

            // Quantity
            _buildTextField(
              label: "Quantity",
              inputType: TextInputType.number,
              onChanged: (val) => setState(() => quantity = int.tryParse(val) ?? 0),
            ),

            // Amount
            _buildTextField(
              label: "Amount per item",
              inputType: TextInputType.number,
              onChanged: (val) => setState(() => amount = double.tryParse(val) ?? 0.0),
            ),

            // GST
            _buildTextField(
              label: "GST",
              inputType: TextInputType.number,
              onChanged: (val) => setState(() => gst = double.tryParse(val) ?? 0.0),
            ),

            // Discount
            _buildTextField(
              label: "Discount",
              inputType: TextInputType.number,
              onChanged: (val) => setState(() => discount = double.tryParse(val) ?? 0.0),
            ),

            const SizedBox(height: 16),
            Divider(thickness: 1),
            const SizedBox(height: 16),

            // Totals
            _buildSummaryRow("Total", total.toStringAsFixed(2)),
            const SizedBox(height: 8),
            _buildSummaryRow("GST", gst.toStringAsFixed(2)),
            const SizedBox(height: 8),
            _buildSummaryRow("Discount", discount.toStringAsFixed(2)),
            const SizedBox(height: 8),
            const Divider(thickness: 1),
            const SizedBox(height: 8),
            _buildSummaryRow("Grand Total", grandTotal.toStringAsFixed(2)),
            const SizedBox(height: 24),

            // Submit button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFF2A738),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                onPressed: () {
                  // Submit order logic
                },
                child: Text(
                  "Submit",
                  style: GoogleFonts.poppins(fontSize: 16, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
