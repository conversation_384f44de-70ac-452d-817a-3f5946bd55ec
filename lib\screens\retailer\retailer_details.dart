import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/retailer_provider.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_details_tab.dart';
import 'package:mr_garments_mobile/widgets/invoice_tab.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class RetailerDetails extends ConsumerStatefulWidget {
  final int retailerId;
  const RetailerDetails({super.key, required this.retailerId});

  @override
  ConsumerState<RetailerDetails> createState() => _RetailerDetailsState();
}

class _RetailerDetailsState extends ConsumerState<RetailerDetails>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Fetch retailer details
    Future.microtask(() {
      ref
          .read(retailersProvider.notifier)
          .fetchRetailerDetails(widget.retailerId);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      foregroundColor: Colors.white,
      elevation: 0,
      title: Row(
        children: [
          const SizedBox(width: 16),
          Text(
            "Retailer Details",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => AdminHomePage()),
                (route) => false,
              );
            },
            child: const Icon(Icons.home, color: Colors.white),
          ),
        ],
      ),
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: [
          Tab(child: Text("Details", style: GoogleFonts.poppins())),
          Tab(child: Text("Invoice", style: GoogleFonts.poppins())),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(12),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.chat_bubble_outline, color: Colors.white),
              label: Text(
                "Chat",
                style: GoogleFonts.poppins(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF005368),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () async {
                await ref
                    .read(retailersProvider.notifier)
                    .deactivateRetailer(widget.retailerId);
                if (!mounted) return;
                AppSnackbar.showSuccess(context, "Retailer deactivated");

                Navigator.pop(context);
              },
              icon: const Icon(Icons.cancel_outlined, color: Colors.white),
              label: Text(
                "Deactivate",
                style: GoogleFonts.poppins(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                RetailerDetailsTab(
                  retailerId: widget.retailerId,
                ), // Replaced with glassmorphic tab
                const InvoiceTab(),
              ],
            ),
          ),
          _buildBottomButtons(),
        ],
      ),
    );
  }
}
