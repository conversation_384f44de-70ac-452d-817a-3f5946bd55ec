import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_garments_mobile/providers/user_provider.dart';
import 'package:mr_garments_mobile/screens/users/add_edit_user.dart';
import 'package:mr_garments_mobile/screens/users/edit_staff.dart';
import 'package:mr_garments_mobile/screens/chat/member_chat_inbox.dart';
import 'package:mr_garments_mobile/services/session_service.dart';

class UsersTab extends ConsumerWidget {
  final String searchQuery;
  const UsersTab({super.key, required this.searchQuery});

  Color getStatusColor(String status) {
    return status == 'rejected' ? Colors.red.shade100 : Colors.green.shade100;
  }

  Color getStatusTextColor(String status) {
    return status == 'rejected' ? Colors.red.shade800 : Colors.green.shade800;
  }

  void _startChatWithUser(
    BuildContext context,
    Map<String, dynamic> user,
  ) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // Create individual chat with this user using enhanced method
      final chatId = await _createChatWithUser(user);

      // Close loading dialog
      if (context.mounted) Navigator.pop(context);

      // Navigate to chat inbox
      if (context.mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => MemberChatInbox(
                  chatId: chatId,
                  chatName: user['name']?.toString() ?? 'Chat',
                  isGroup: false, // Individual chat
                ),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) Navigator.pop(context);

      // Show error
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting chat: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<String> _createChatWithUser(Map<String, dynamic> user) async {
    final currentUserId = await SessionService.getUserId();
    if (currentUserId == null) throw Exception('User not logged in');

    final otherUserId = user['id']?.toString() ?? '';
    if (otherUserId.isEmpty) throw Exception('Invalid user ID');

    // Generate chat ID by combining user IDs
    final chatId = _generateChatId(currentUserId.toString(), otherUserId);

    // Check if chat already exists
    final existingChat =
        await FirebaseFirestore.instance.collection('chats').doc(chatId).get();

    if (existingChat.exists) {
      return chatId;
    }

    // Get current user info from session
    final currentUserName = await SessionService.getUserName() ?? 'Admin';
    final currentUserEmail = await SessionService.getUserEmail() ?? '';
    final currentUserRole = await SessionService.getUserRole() ?? 'admin';

    // Create chat users if they don't exist
    await _ensureChatUserExists(
      currentUserId.toString(),
      currentUserName,
      currentUserEmail,
      role: currentUserRole,
    );

    await _ensureChatUserExists(
      otherUserId,
      user['name']?.toString() ?? 'User',
      user['email']?.toString() ?? '',
      role: user['account_type']?.toString().toLowerCase() ?? 'user',
    );

    // Create new chat
    final now = DateTime.now();
    final chatData = {
      'id': chatId,
      'type': 'individual',
      'memberIds': [currentUserId.toString(), otherUserId],
      'memberNames': {
        currentUserId.toString(): currentUserName,
        otherUserId: user['name']?.toString() ?? 'User',
      },
      'memberProfileUrls': {currentUserId.toString(): '', otherUserId: ''},
      'isActive': true,
      'lastMessage': null,
      'lastMessageTime': null,
      'lastMessageSenderId': '',
      'unreadCounts': {currentUserId.toString(): 0, otherUserId: 0},
      'createdAt': now.millisecondsSinceEpoch,
      'updatedAt': now.millisecondsSinceEpoch,
    };

    await FirebaseFirestore.instance
        .collection('chats')
        .doc(chatId)
        .set(chatData);

    return chatId;
  }

  Future<void> _ensureChatUserExists(
    String userId,
    String name,
    String email, {
    String? role,
  }) async {
    final userDoc =
        await FirebaseFirestore.instance.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      final now = DateTime.now();
      await FirebaseFirestore.instance.collection('users').doc(userId).set({
        'id': userId,
        'name': name,
        'email': email,
        'role': role ?? 'user', // Default role if not provided
        'profileImageUrl': '',
        'isOnline': false,
        'lastSeen': now.millisecondsSinceEpoch,
        'createdAt': now.millisecondsSinceEpoch,
      });
    }
  }

  String _generateChatId(String userId1, String userId2) {
    final sortedIds = [userId1, userId2]..sort();
    return '${sortedIds[0]}_${sortedIds[1]}';
  }

  Future<Map<String, dynamic>> _getUserRoleAndId() async {
    final role = await SessionService.getUserRole();
    final id = await SessionService.getUserId();
    return {'role': role, 'id': id};
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersAsync = ref.watch(usersProvider).users;
    return usersAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, _) => Center(child: Text("Error: $e")),
      data: (users) {
        return FutureBuilder<Map<String, dynamic>>(
          future: _getUserRoleAndId(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            final currentUserRole = snapshot.data?['role'] as String?;
            final currentUserId = snapshot.data?['id'] as int?;

            final activeUsers =
                users.where((u) {
                  final status = u['status']?.toLowerCase();
                  return status == 'approved' || status == 'active' || status == 'approve';
                }).toList();

            // Apply role-based filtering
            List<Map<String, dynamic>> roleFilteredUsers;
            if (currentUserRole == 'admin') {
              // Admin can see all users except themselves
              roleFilteredUsers =
                  activeUsers
                      .where(
                        (u) => u['id']?.toString() != currentUserId?.toString(),
                      )
                      .cast<Map<String, dynamic>>()
                      .toList();
            } else {
              // Non-admin users can only see admin users
              roleFilteredUsers =
                  activeUsers
                      .where(
                        (u) =>
                            u['account_type']?.toString().toLowerCase() ==
                            'admin',
                      )
                      .cast<Map<String, dynamic>>()
                      .toList();
            }

            // Apply search filter
            final filteredUsers =
                roleFilteredUsers.where((u) {
                  final name = (u['name'] ?? '').toLowerCase();
                  return name.contains(searchQuery.toLowerCase());
                }).toList();

            if (filteredUsers.isEmpty) {
              return const Center(child: Text("No users found"));
            }

            return ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              itemCount: filteredUsers.length,
              itemBuilder: (context, index) {
                final user = filteredUsers[index];

                return Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 3,
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 14,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const CircleAvatar(
                          radius: 18,

                          backgroundColor: Color(0xFF005368),
                          child: Icon(Icons.person, color: Colors.white),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                user['name'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                user['role'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.blueGrey,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                user['email'] ?? '',
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  color: Colors.grey[700],
                                ),
                              ),
                              const SizedBox(height: 8),
                              if (user['status'] != null)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: getStatusColor(
                                      user['status'] ?? 'unknown',
                                    ),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    (user['status']?.toLowerCase() ==
                                                'active' ||
                                            user['status']?.toLowerCase() ==
                                                'approved')
                                        ? 'Active'
                                        : user['status'],
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: getStatusTextColor(user['status']),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // IconButton(
                            //   onPressed:
                            //       () => _startChatWithUser(context, user),
                            //   icon: const Icon(
                            //     Icons.chat,
                            //     color: Color(0xFF005368),
                            //   ),
                            //   tooltip: 'Start Chat',
                            // ),
                            IconButton(
                              onPressed: () async {
                                // Check if user is staff to navigate to appropriate edit screen
                                final accountType =
                                    user['account_type']
                                        ?.toString()
                                        .toLowerCase() ??
                                    '';
                                final jobRole =
                                    user['job_role']
                                        ?.toString()
                                        .toLowerCase() ??
                                    '';
                                final role =
                                    user['role']?.toString().toLowerCase() ??
                                    '';

                                // Check multiple possible fields for staff identification
                                if (accountType == 'staff' ||
                                    jobRole == 'staff' ||
                                    role == 'staff') {
                                  // Navigate to Edit Staff screen
                                  await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => EditStaff(staff: user),
                                    ),
                                  );
                                } else {
                                  // Navigate to regular Edit User screen
                                  await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => AddEditUser(user: user),
                                    ),
                                  );
                                }
                              },
                              icon: const Icon(
                                Icons.edit,
                                color: Color(0xFFF2A738),
                              ),
                              tooltip: 'Edit User',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }
}
