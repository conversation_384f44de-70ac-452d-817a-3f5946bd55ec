import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class BrandsSection extends StatelessWidget {
  final List<String> brandImages;
  final VoidCallback? onViewAll;
  final int? maxItems;
  const BrandsSection({
    super.key,
    required this.brandImages,
    this.onViewAll,
    this.maxItems = 6,
  });

  Widget _buildBrandGridItem(String imagePath) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(55),
            blurRadius: 6,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: Colors.white.withAlpha(102)),
      ),
      padding: EdgeInsets.all(12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(imagePath, width: 90, height: 90, fit: BoxFit.cover),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final displayItems =
        (maxItems != null && maxItems! > 0 && brandImages.length > maxItems!)
            ? brandImages.sublist(0, maxItems)
            : brandImages;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'BRANDS',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF005368),
              ),
            ),
            if (onViewAll != null) 
            GestureDetector(
              onTap: onViewAll,
              child: Text(
                'View All',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: const Color(0xFFF2A738),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 10),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: displayItems.length,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 10,
            crossAxisSpacing: 10,
            childAspectRatio: 1,
          ),
          itemBuilder:
              (context, index) => _buildBrandGridItem(brandImages[index]),
        ),
      ],
    );
  }
}
