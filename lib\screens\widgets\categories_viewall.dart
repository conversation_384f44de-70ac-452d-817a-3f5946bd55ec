import 'package:flutter/material.dart';
import 'package:mr_garments_mobile/screens/widgets/categories_section.dart';

class CategoriesViewall extends StatelessWidget {
  const CategoriesViewall({super.key});

  @override
  Widget build(BuildContext context) {
      final List<Map<String, String>> categoryData = [
    {'image': 'assets/images/categories/category1.jpg', 'title': 'Tops'},
    {'image': 'assets/images/categories/category2.jpg', 'title': 'T-shirts'},
    {'image': 'assets/images/categories/category3.jpg', 'title': 'Shirts'},
    {'image': 'assets/images/categories/category4.jpg', 'title': 'Denim'},
    {'image': 'assets/images/categories/category5.jpg', 'title': 'Shorts'},
    {'image': 'assets/images/categories/category6.jpg', 'title': 'Kurtis'},
    {'image': 'assets/images/categories/category7.jpg', 'title': 'Jean<PERSON>'},
  ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Categories'),
        foregroundColor: Colors.white,
        backgroundColor: const Color(0xFF005368),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(14),
        child: CategoriesSection(categories: categoryData,maxItems: null,),
      ),
    );
  }
}