import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/screens/admin/account_page.dart';
import 'package:mr_garments_mobile/screens/admin/side_menubar.dart';
import 'package:mr_garments_mobile/screens/brand/brand_pageview.dart';
import 'package:mr_garments_mobile/screens/catalog/catalog_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/categories/categories_pageview.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/ledgers/ledger_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/manufacturer/manufacturer_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/order/order_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_tabpage_view.dart';
import 'package:mr_garments_mobile/screens/users/users_screen.dart';
import 'package:mr_garments_mobile/screens/chat/chat_main_page.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/models/chat_user.dart';

class AdminHomePage extends StatefulWidget {
  const AdminHomePage({super.key});

  @override
  State<AdminHomePage> createState() => _AdminHomePageState();
}

class _AdminHomePageState extends State<AdminHomePage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int _selectedIndex = 0;
  bool _showWelcomeCard = true;
  String _adminName = 'Admin'; // Default fallback name

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(seconds: 10), () {
      setState(() {
        _showWelcomeCard = false;
      });
    });
    _initializeChatUser();
    _loadAdminName();
  }

  void _loadAdminName() async {
    try {
      final userName = await SessionService.getUserName();
      if (userName != null && userName.isNotEmpty) {
        setState(() {
          _adminName = userName;
        });
      }
    } catch (e) {
      // Keep default name if there's an error
    }
  }

  void _initializeChatUser() async {
    try {
      final userId = await SessionService.getUserId();
      final userName = await SessionService.getUserName();
      final userEmail = await SessionService.getUserEmail();

      if (userId != null && userName != null && userEmail != null) {
        final chatUser = ChatUser(
          id: userId.toString(),
          name: userName,
          email: userEmail,
          role: 'admin',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await ChatService.createOrUpdateUser(chatUser);
      }
    } catch (e) {
      // Silently handle error - chat functionality will still work
      // Error is ignored to prevent disrupting the admin home page
    }
  }

  void _onItemTapped(int index) {
    if (index == 0) {
      setState(() {
        _selectedIndex = 0;
      });
      // Home - stay on current page
    } else if (index == 1) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const OrderTabPageView()),
      ).then((_) {
        setState(() {
          _selectedIndex = 0;
        });
      });
    } else if (index == 2) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const ChatMainPage()),
      ).then((_) {
        setState(() {
          _selectedIndex = 0;
        });
      });
    } else if (index == 3) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const AccountPage()),
      ).then((_) {
        setState(() {
          _selectedIndex = 0;
        });
      });
    }
  }

  void _navigateTo(String name) {
    // Replace with actual navigation logic
    if (name == 'Users') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => UsersScreen()));
    } else if (name == 'Manufacturer') {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => ManufacturerTabpageView()),
      );
    } else if (name == 'Retailer') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => RetailerTabpageView()));
    } else if (name == 'Distributor') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => DistributorTabpageView()));
    } else if (name == 'Ledger') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => LedgerTabpageView()));
    } else if (name == 'Catalog') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => CatalogTabpageView()));
    } else if (name == 'Brand') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => BrandPageview()));
    } else if (name == 'Category') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => CategoriesPageView()));
    } else if (name == 'Orders') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => OrderTabPageView()));
    } else if (name == 'Chat') {
      Navigator.of(
        context,
      ).push(MaterialPageRoute(builder: (context) => ChatMainPage()));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      drawer: SideMenubar(),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(90),
        child: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xFF00536B),
          elevation: 4,
          shape: const RoundedRectangleBorder(),
          flexibleSpace: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => _scaffoldKey.currentState?.openDrawer(),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(8),
                      child: const Icon(Icons.menu, color: Color(0xFF00536B)),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Search...',
                        hintStyle: GoogleFonts.poppins(color: Colors.white70),
                        prefixIcon: const Icon(
                          Icons.search,
                          color: Colors.white,
                        ),
                        filled: true,
                        fillColor: Colors.white.withAlpha(32),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                      ),
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),

                  const SizedBox(width: 10),
                  const Icon(
                    Icons.notifications_none,
                    color: Colors.white,
                    size: 25,
                  ),
                  const SizedBox(width: 10),
                  // const CircleAvatar(
                  //   backgroundImage: AssetImage('assets/images/profile.jpg'),
                  //   radius: 18,
                  // ),
                ],
              ),
            ),
          ),
        ),
      ),
      body: Container(
        padding: const EdgeInsets.all(14),
        child: Column(
          children: [
            AnimatedOpacity(
              opacity: _showWelcomeCard ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 500),
              child:
                  _showWelcomeCard
                      ? Container(
                        margin: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: const Color.fromARGB(255, 238, 236, 236),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(20),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                          border: Border.all(color: Colors.white.withAlpha(50)),
                        ),
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              ' Hi, $_adminName',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFFF2A738),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Hope you’re having a great day!',
                              style: GoogleFonts.poppins(
                                fontSize: 22,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF005368),
                              ),
                            ),
                          ],
                        ),
                      )
                      : const SizedBox.shrink(),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: GridView.count(
                crossAxisCount: 3,
                crossAxisSpacing: 10,
                mainAxisSpacing: 10,
                children: [
                  _GridTile(
                    title: 'Users',
                    icon: LucideIcons.users,
                    onTap: () => _navigateTo('Users'),
                  ),
                  _GridTile(
                    title: 'Manufacturer',
                    icon: LucideIcons.userCog,
                    onTap: () => _navigateTo('Manufacturer'),
                  ),
                  _GridTile(
                    title: 'Retailer',
                    icon: LucideIcons.shoppingBag,
                    onTap: () => _navigateTo('Retailer'),
                  ),
                  _GridTile(
                    title: 'Distributor',
                    icon: LucideIcons.userCheck,
                    onTap: () => _navigateTo('Distributor'),
                  ),
                  _GridTile(
                    title: 'Ledger',
                    icon: LucideIcons.dollarSign,
                    onTap: () => _navigateTo('Ledger'),
                  ),
                  _GridTile(
                    title: 'Orders',
                    icon: LucideIcons.edit,
                    onTap: () => _navigateTo('Orders'),
                  ),
                  _GridTile(
                    title: 'Catalog',
                    icon: LucideIcons.bookOpen,
                    onTap: () => _navigateTo('Catalog'),
                  ),
                  _GridTile(
                    title: 'Category',
                    icon: LucideIcons.grid,
                    onTap: () => _navigateTo('Category'),
                  ),
                  _GridTile(
                    title: 'Brand',
                    icon: LucideIcons.tag,
                    onTap: () => _navigateTo('Brand'),
                  ),
                  _GridTile(
                    title: 'Chat',
                    icon: LucideIcons.messageSquare,
                    onTap: () => _navigateTo('Chat'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        selectedItemColor: const Color(0xFF00536B),
        unselectedItemColor: Colors.grey,
        onTap: _onItemTapped,
        showUnselectedLabels: true,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(LucideIcons.edit), label: 'Order'),
          BottomNavigationBarItem(
            icon: Icon(LucideIcons.messageSquare),
            label: 'chat',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            label: 'Account',
          ),
        ],
      ),
    );
  }
}

class _GridTile extends StatefulWidget {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  const _GridTile({
    required this.title,
    required this.icon,
    required this.onTap,
    // super.key,
  });

  @override
  State<_GridTile> createState() => _GridTileState();
}

class _GridTileState extends State<_GridTile>
    with SingleTickerProviderStateMixin {
  double _scale = 1.0;

  void _onTapDown(TapDownDetails details) {
    setState(() => _scale = 0.97);
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _scale = 1.0);
  }

  void _onTapCancel() {
    setState(() => _scale = 1.0);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      child: AnimatedScale(
        scale: _scale,
        duration: const Duration(milliseconds: 150),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFFE8F0FA), Color(0xFFF5FAFF)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(18),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withAlpha(55),
                blurRadius: 8,
                offset: Offset(0, 4),
              ),
            ],
          ),
          padding: const EdgeInsets.all(6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF00536B).withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  widget.icon,
                  size: 28,
                  color: const Color(0xFF00536B),
                ),
              ),
              const SizedBox(height: 10),
              Text(
                widget.title,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFF00536B),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
