import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:lucide_icons/lucide_icons.dart';

/// Utility functions for handling image URLs and processing
class ImageUtils {
  /// Cleans malformed image URLs that have double storage paths
  ///
  /// This fixes URLs like:
  /// https://mrgarment.braincavesoft.com/storage/https://mrgarment.braincavesoft.com/storage/catalogs/...
  ///
  /// And converts them to:
  /// https://mrgarment.braincavesoft.com/storage/catalogs/...
  static String cleanImageUrl(String url) {
    if (url.startsWith(
      'https://mrgarment.braincavesoft.com/storage/https://',
    )) {
      return url.replaceFirst(
        'https://mrgarment.braincavesoft.com/storage/',
        '',
      );
    }
    return url;
  }

  /// Cleans a list of image URLs
  static List<String> cleanImageUrls(List<String> urls) {
    return urls.map((url) => cleanImageUrl(url)).toList();
  }

  /// Builds a cached network image widget with proper error handling and loading states
  static Widget buildNetworkImage(
    String imageUrl, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    BorderRadius? borderRadius,
  }) {
    final cleanUrl = cleanImageUrl(imageUrl);

    return CachedNetworkImage(
      imageUrl: cleanUrl,
      fit: fit,
      width: width,
      height: height,
      placeholder:
          (context, url) => Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            // child: const Center(
            //   child: CircularProgressIndicator(
            //     valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF005368)),
            //     strokeWidth: 2,
            //   ),
            // ),
          ),
      errorWidget: (context, url, error) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: borderRadius,
          ),
          child: const Center(
            child: Icon(LucideIcons.image, color: Colors.grey, size: 24),
          ),
        );
      },
    );
  }

  /// Builds a cached network image widget specifically for chat messages
  static Widget buildChatImage(
    String imageUrl, {
    required double width,
    required double height,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    final cleanUrl = cleanImageUrl(imageUrl);

    final imageWidget = CachedNetworkImage(
      imageUrl: cleanUrl,
      fit: fit,
      width: width,
      height: height,
      placeholder:
          (context, url) => Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            // child: const Center(
            //   child: CircularProgressIndicator(
            //     valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF005368)),
            //     strokeWidth: 2,
            //   ),
            // ),
          ),
      errorWidget: (context, url, error) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: borderRadius,
          ),
          child: const Center(
            child: Icon(LucideIcons.image, color: Colors.grey, size: 32),
          ),
        );
      },
    );

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: imageWidget);
    }

    return imageWidget;
  }

  /// Builds a cached network image widget specifically for catalog images
  static Widget buildCatalogImage(
    String imageUrl, {
    BoxFit fit = BoxFit.cover,
    double? width,
    double? height,
    BorderRadius? borderRadius,
  }) {
    return CachedNetworkImage(
      imageUrl: cleanImageUrl(imageUrl),
      fit: fit,
      width: width,
      height: height,
      placeholder:
          (context, url) => Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            // child: const Center(
            //   child: CircularProgressIndicator(
            //     valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF005368)),
            //     strokeWidth: 2,
            //   ),
            // ),
          ),
      errorWidget:
          (context, url, error) => Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            child: const Center(
              child: Icon(LucideIcons.image, color: Colors.grey, size: 24),
            ),
          ),
      // Cache configuration optimized for catalog images
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
      maxWidthDiskCache: 1200,
      maxHeightDiskCache: 1200,
    );
  }

  /// Preload images for better performance
  /// This is useful for preloading chat images when opening a chat
  static Future<void> preloadChatImages(List<String> imageUrls) async {
    for (final url in imageUrls) {
      try {
        await DefaultCacheManager().getSingleFile(cleanImageUrl(url));
      } catch (e) {
        // Silently ignore errors during preloading
        continue;
      }
    }
  }

  /// Preload a single image
  static Future<void> preloadImage(String imageUrl) async {
    try {
      await DefaultCacheManager().getSingleFile(cleanImageUrl(imageUrl));
    } catch (e) {
      // Silently ignore errors during preloading
    }
  }

  /// Check if an image is cached
  static Future<bool> isImageCached(String imageUrl) async {
    try {
      final fileInfo = await DefaultCacheManager().getFileFromCache(
        cleanImageUrl(imageUrl),
      );
      return fileInfo != null;
    } catch (e) {
      return false;
    }
  }
}
