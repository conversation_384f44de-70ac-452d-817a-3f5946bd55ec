import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;
import 'package:mr_garments_mobile/models/message.dart';
import 'package:mr_garments_mobile/models/chat.dart';
import 'package:mr_garments_mobile/providers/chat_provider.dart';
import 'package:mr_garments_mobile/screens/chat/highlightable_message.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/message_actions_bottom_sheet.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/utils/image_utils.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';
import 'package:url_launcher/url_launcher.dart';

class MemberChatInbox extends ConsumerStatefulWidget {
  final String chatId;
  final String chatName;
  final bool isGroup;

  const MemberChatInbox({
    super.key,
    required this.chatId,
    required this.chatName,
    required this.isGroup,
  });

  @override
  ConsumerState<MemberChatInbox> createState() => _MemberChatInboxState();
}

class _MemberChatInboxState extends ConsumerState<MemberChatInbox>
    with WidgetsBindingObserver {
  // For reply highlight/scroll
  final Map<String, GlobalKey<HighlightableMessageState>> _messageKeys = {};
  String? _highlightedMessageId;

  void _scrollToAndHighlightMessage(String messageId) {
    final key = _messageKeys[messageId];
    if (key != null && key.currentContext != null) {
      // If already built, just ensure visible and highlight
      Scrollable.ensureVisible(
        key.currentContext!,
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        alignment: 0.5,
      );
      key.currentState?.triggerHighlight();
    } else {
      // If not built (far away), scroll to index first, then ensureVisible
      final messages = ref
          .read(messagesStreamProvider(widget.chatId))
          .maybeWhen(data: (msgs) => msgs, orElse: () => <Message>[]);
      final index = messages.indexWhere((m) => m.id == messageId);
      if (index != -1) {
        // Because ListView is reversed
        final itemExtent = 90.0; // Approximate height of a message bubble
        final offset = index * itemExtent;
        _scrollController.animateTo(
          offset,
          duration: const Duration(milliseconds: 400),
          curve: Curves.easeInOut,
        );
        Future.delayed(const Duration(milliseconds: 450), () {
          final key2 = _messageKeys[messageId];
          if (key2 != null && key2.currentContext != null) {
            Scrollable.ensureVisible(
              key2.currentContext!,
              duration: const Duration(milliseconds: 400),
              curve: Curves.easeInOut,
              alignment: 0.5,
            );
            key2.currentState?.triggerHighlight();
          }
        });
      }
    }
  }

  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _imagePicker = ImagePicker();
  String? _currentUserId;
  bool _isTyping = false;
  DateTime? _lastTypingEvent;

  // Multi-image selection state
  List<XFile> _selectedImages = [];

  // Image sending state
  bool _isSendingImage = false;
  bool _isSendingFromCamera = false;
  bool _isSendingFromGallery = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadCurrentUser();
    _markMessagesAsRead();
    // Online status is now handled globally by OnlineStatusService
    _messageController.addListener(_handleTyping);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // Mark messages as read when app becomes active (user returns to app)
    if (state == AppLifecycleState.resumed && mounted) {
      _markMessagesAsRead();
    }
  }

  void _markMessagesAsRead() async {
    final currentUserId = await SessionService.getUserId();
    if (currentUserId != null) {
      final messageNotifier = ref.read(messageProvider(widget.chatId).notifier);
      // Mark all messages in this chat as read
      messageNotifier.markAllMessagesAsRead(currentUserId.toString());
    }
  }

  void _handlePermissionError() async {
    // Auto-retry mechanism for permission errors
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      await ChatService.switchUser();
      await Future.delayed(const Duration(milliseconds: 1000));

      // Invalidate the messages provider to trigger a refresh
      if (mounted) {
        ref.invalidate(messagesStreamProvider(widget.chatId));
      }
    } catch (e) {
      // Silently handle auto-retry errors
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _setTypingStatus(false);
    _messageController.removeListener(_handleTyping);
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _handleTyping() async {
    if (_currentUserId == null) return;
    final isNowTyping = _messageController.text.trim().isNotEmpty;
    if (isNowTyping != _isTyping) {
      _isTyping = isNowTyping;
      _setTypingStatus(_isTyping);
    }
    // Optionally, add a debounce to set to false after a delay
    if (_isTyping) {
      _lastTypingEvent = DateTime.now();
      Future.delayed(const Duration(seconds: 2), () {
        if (_lastTypingEvent != null &&
            DateTime.now().difference(_lastTypingEvent!) >=
                const Duration(seconds: 2)) {
          if (_messageController.text.trim().isEmpty) {
            _setTypingStatus(false);
          }
        }
      });
    }
  }

  void _setTypingStatus(bool isTyping) async {
    if (_currentUserId == null) return;
    await ChatService.setTypingStatus(widget.chatId, _currentUserId!, isTyping);
  }

  void _loadCurrentUser() async {
    final userId = await SessionService.getUserId();
    setState(() {
      _currentUserId = userId?.toString();
    });
  }

  String _getOnlineStatus(AsyncValue<Chat?> chatAsync) {
    return chatAsync.when(
      data: (chat) {
        if (chat == null || _currentUserId == null) return 'Offline';

        // Get other user ID
        final otherUserId = chat.memberIds.firstWhere(
          (id) => id != _currentUserId,
          orElse: () => '',
        );

        if (otherUserId.isEmpty) return 'Offline';

        // Watch other user's online status
        final otherUserAsync = ref.watch(otherUserProvider(otherUserId));
        return otherUserAsync.when(
          data: (user) {
            if (user?.isOnline == true) {
              return 'Online';
            } else if (user?.lastSeen != null) {
              final lastSeen = user!.lastSeen!;
              final now = DateTime.now();
              final difference = now.difference(lastSeen);

              if (difference.inMinutes < 1) {
                return 'Just now';
              } else if (difference.inMinutes < 60) {
                return '${difference.inMinutes}m ago';
              } else if (difference.inHours < 24) {
                return '${difference.inHours}h ago';
              } else {
                return '${difference.inDays}d ago';
              }
            }
            return 'Offline';
          },
          loading: () => 'Loading...',
          error: (_, __) => 'Offline',
        );
      },
      loading: () => 'Loading...',
      error: (_, __) => 'Offline',
    );
  }

  String _getGroupSubtitle(AsyncValue<Chat?> chatAsync) {
    return chatAsync.when(
      data: (chat) {
        if (chat == null) return 'Group chat';

        final memberCount = chat.memberIds.length;
        return 'Broadcast to $memberCount members';
      },
      loading: () => 'Loading...',
      error: (_, __) => 'Group chat',
    );
  }

  void _sendTextMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    final messageNotifier = ref.read(messageProvider(widget.chatId).notifier);
    final messageState = ref.read(messageProvider(widget.chatId));

    _messageController.clear();

    final success = await messageNotifier.sendTextMessage(
      text,
      replyToMessageId: messageState.replyToMessage?.id,
    );

    if (!success && mounted) {
      AppSnackbar.showError(context, 'Failed to send message');
    }

    _scrollToBottom();
  }

  void _pickImagesFromGallery() async {
    // Prevent multiple simultaneous gallery operations
    if (_isSendingFromGallery) return;

    try {
      setState(() {
        _isSendingFromGallery = true;
      });

      final List<XFile>? images = await _imagePicker.pickMultiImage(
        imageQuality: 80,
      );
      if (images != null && images.isNotEmpty) {
        setState(() {
          _selectedImages = images;
        });

        if (mounted) {
          AppSnackbar.showInfo(
            context,
            '${images.length} image${images.length > 1 ? 's' : ''} selected',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(context, 'Error picking images: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSendingFromGallery = false;
        });
      }
    }
  }

  /// Compress image for better performance and smaller file size
  Future<File?> _compressImage(File imageFile) async {
    try {
      final dir = Directory.systemTemp;
      final targetPath = path.join(
        dir.path,
        '${DateTime.now().millisecondsSinceEpoch}_compressed.jpg',
      );

      final compressedFile = await FlutterImageCompress.compressAndGetFile(
        imageFile.absolute.path,
        targetPath,
        quality: 70, // Good balance between quality and file size
        minWidth: 1024,
        minHeight: 1024,
        format: CompressFormat.jpeg,
      );

      return compressedFile != null ? File(compressedFile.path) : imageFile;
    } catch (e) {
      // If compression fails, return original file
      return imageFile;
    }
  }

  void _sendSelectedImages() async {
    if (_selectedImages.isEmpty || _isSendingImage) return;

    setState(() {
      _isSendingImage = true;
    });

    try {
      final messageNotifier = ref.read(messageProvider(widget.chatId).notifier);
      final messageState = ref.read(messageProvider(widget.chatId));
      bool allSuccess = true;

      for (final image in _selectedImages) {
        // Compress image before sending
        final compressedImage = await _compressImage(File(image.path));
        if (compressedImage != null) {
          final success = await messageNotifier.sendImageMessage(
            compressedImage,
            replyToMessageId: messageState.replyToMessage?.id,
          );
          if (!success) allSuccess = false;
        } else {
          allSuccess = false;
        }
      }

      setState(() {
        _selectedImages.clear();
      });

      if (mounted) {
        if (allSuccess) {
          AppSnackbar.showSuccess(context, 'Images sent successfully');
        } else {
          AppSnackbar.showError(context, 'Failed to send some images');
        }
      }
      _scrollToBottom();
    } finally {
      if (mounted) {
        setState(() {
          _isSendingImage = false;
        });
      }
    }
  }

  void _sendCameraImage() async {
    // Prevent multiple simultaneous camera operations
    if (_isSendingFromCamera) return;

    try {
      setState(() {
        _isSendingFromCamera = true;
      });

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // Show immediate feedback that image is being processed
        if (mounted) {
          AppSnackbar.showInfo(context, 'Processing image...');
        }

        setState(() {
          _isSendingImage = true;
        });

        final messageNotifier = ref.read(
          messageProvider(widget.chatId).notifier,
        );
        final messageState = ref.read(messageProvider(widget.chatId));

        // Compress image before sending
        final compressedImage = await _compressImage(File(image.path));

        if (compressedImage != null) {
          final success = await messageNotifier.sendImageMessage(
            compressedImage,
            replyToMessageId: messageState.replyToMessage?.id,
          );

          if (mounted) {
            if (success) {
              AppSnackbar.showSuccess(context, 'Image sent successfully');
            } else {
              AppSnackbar.showError(context, 'Failed to send image');
            }
          }
        } else {
          if (mounted) {
            AppSnackbar.showError(context, 'Failed to process image');
          }
        }

        _scrollToBottom();
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(context, 'Error taking photo: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSendingImage = false;
          _isSendingFromCamera = false;
        });
      }
    }
  }

  void _sendFileMessage() async {
    try {
      final FilePickerResult? result = await FilePicker.platform.pickFiles();

      if (result != null && result.files.single.path != null) {
        final messageNotifier = ref.read(
          messageProvider(widget.chatId).notifier,
        );
        final messageState = ref.read(messageProvider(widget.chatId));

        final success = await messageNotifier.sendFileMessage(
          File(result.files.single.path!),
          replyToMessageId: messageState.replyToMessage?.id,
        );

        if (!success && mounted) {
          AppSnackbar.showError(context, 'Failed to send file');
        }

        _scrollToBottom();
      }
    } catch (e) {
      if (mounted) {
        AppSnackbar.showError(context, 'Error picking file: $e');
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _onMessageLongPress(Message message) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder:
          (context) => MessageActionsBottomSheet(
            message: message,
            chatId: widget.chatId,
            isGroup: widget.isGroup,
          ),
    );
  }

  void _onReplyToMessage(Message message) {
    final messageNotifier = ref.read(messageProvider(widget.chatId).notifier);
    messageNotifier.setReplyToMessage(message);
  }

  String _getMessageDisplayText(Message message) {
    // First check if we have text content regardless of type
    if (message.text != null && message.text!.isNotEmpty) {
      return message.text!;
    }

    // Then handle specific media types
    switch (message.type) {
      case MessageType.image:
        return '📷 Image';
      case MessageType.file:
        return '📎 ${message.fileName ?? 'File'}';
      case MessageType.audio:
        return '🎵 Audio';
      case MessageType.video:
        return '🎥 Video';
      case MessageType.location:
        return '📍 Location';
      case MessageType.contact:
        return '👤 Contact';
      case MessageType.catalog:
        return '📋 Catalog';
      case MessageType.text:
      default:
        // Fallback - this should rarely be reached now
        return message.text ?? 'Message';
    }
  }

  Widget _buildSwipeableMessage(
    Message message,
    bool isMe,
    bool showSenderName,
  ) {
    return _SwipeToReply(
      message: message,
      isMe: isMe,
      onReply: () => _onReplyToMessage(message),
      onLongPress: () => _onMessageLongPress(message),
      child: _buildMessageBubble(message, isMe, showSenderName),
    );
  }

  @override
  Widget build(BuildContext context) {
    final messagesAsync = ref.watch(messagesStreamProvider(widget.chatId));
    final messageState = ref.watch(messageProvider(widget.chatId));
    final chatAsync = ref.watch(chatDetailProvider(widget.chatId));

    // Listen for new messages and mark them as read automatically
    ref.listen<AsyncValue<List<Message>>>(
      messagesStreamProvider(widget.chatId),
      (previous, next) {
        // Only mark as read if we have messages and the user is in the chat
        if (next.hasValue && mounted) {
          _markMessagesAsRead();

          // Preload images for better performance
          final messageNotifier = ref.read(
            messageProvider(widget.chatId).notifier,
          );
          messageNotifier.preloadImagesFromMessages(next.value!);
        }
      },
    );

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(chatAsync),
      body: Column(
        children: [
          // Reply bar (if replying to a message)
          if (messageState.replyToMessage != null)
            _buildReplyBar(messageState.replyToMessage!),

          // Messages list
          Expanded(
            child: messagesAsync.when(
              data: (messages) => _buildMessagesList(messages),
              loading:
                  () => const Center(
                    // child: CircularProgressIndicator(color: Color(0xFF005368)),
                  ),
              error: (error, stack) {
                // Auto-retry for permission errors
                final errorString = error.toString();
                if (errorString.contains('permission-denied')) {
                  _handlePermissionError();
                }
                return _buildErrorState(errorString);
              },
            ),
          ),

          // Message input bar
          _buildMessageInputBar(),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(AsyncValue<Chat?> chatAsync) {
    final typingStatusAsync = ref.watch(typingStatusProvider(widget.chatId));
    String subtitle =
        widget.isGroup
            ? _getGroupSubtitle(chatAsync)
            : _getOnlineStatus(chatAsync);
    if (!widget.isGroup) {
      typingStatusAsync.whenData((typingMap) {
        if (_currentUserId != null) {
          final othersTyping =
              typingMap.entries
                  .where((e) => e.key != _currentUserId && e.value)
                  .isNotEmpty;
          if (othersTyping) {
            subtitle = 'Typing...';
          }
        }
      });
    }
    Widget avatarWidget;
    if (widget.isGroup) {
      avatarWidget = CircleAvatar(
        radius: 18,
        backgroundColor: Colors.white.withOpacity(0.2),
        child: Icon(LucideIcons.users, color: Colors.white, size: 18),
      );
    } else {
      // Individual chat: show other user's profile image if available
      final chat = chatAsync.asData?.value;
      String? otherUserId;
      if (chat != null && _currentUserId != null) {
        otherUserId = chat.memberIds.firstWhere(
          (id) => id != _currentUserId,
          orElse: () => '',
        );
      }
      if (otherUserId != null && otherUserId.isNotEmpty) {
        final otherUserAsync = ref.watch(otherUserProvider(otherUserId));
        avatarWidget = otherUserAsync.when(
          data: (user) {
            if (user != null &&
                user.profileImageUrl != null &&
                user.profileImageUrl!.isNotEmpty) {
              return CircleAvatar(
                radius: 18,
                backgroundColor: Colors.white.withOpacity(0.2),
                backgroundImage: NetworkImage(user.profileImageUrl!),
              );
            } else {
              return CircleAvatar(
                radius: 18,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: Icon(LucideIcons.user, color: Colors.white, size: 18),
              );
            }
          },
          loading:
              () => CircleAvatar(
                radius: 18,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: const CircularProgressIndicator(strokeWidth: 2),
              ),
          error:
              (_, __) => CircleAvatar(
                radius: 18,
                backgroundColor: Colors.white.withOpacity(0.2),
                child: Icon(LucideIcons.user, color: Colors.white, size: 18),
              ),
        );
      } else {
        avatarWidget = CircleAvatar(
          radius: 18,
          backgroundColor: Colors.white.withOpacity(0.2),
          child: Icon(LucideIcons.user, color: Colors.white, size: 18),
        );
      }
    }
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          avatarWidget,
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.chatName,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        PopupMenuButton<String>(
          icon: const Icon(LucideIcons.moreVertical, color: Colors.white),
          onSelected: (value) {
            switch (value) {
              case 'clear':
                _showClearChatDialog();
                break;
              case 'delete':
                _showDeleteChatDialog();
                break;
            }
          },
          itemBuilder:
              (context) => [
                PopupMenuItem(
                  value: 'clear',
                  child: Row(
                    children: [
                      const Icon(LucideIcons.trash2, size: 18),
                      const SizedBox(width: 12),
                      Text('Clear Chat', style: GoogleFonts.poppins()),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      const Icon(
                        LucideIcons.trash,
                        size: 18,
                        color: Colors.red,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        widget.isGroup ? 'Delete Group' : 'Delete Chat',
                        style: GoogleFonts.poppins(color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ],
        ),
      ],
    );
  }

  Widget _buildReplyBar(Message replyMessage) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF005368).withOpacity(0.1),
        border: Border(
          left: BorderSide(color: const Color(0xFF005368), width: 4),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Replying to ${replyMessage.senderName}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF005368),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getMessageDisplayText(replyMessage),
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[700],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 18),
            onPressed: () {
              final messageNotifier = ref.read(
                messageProvider(widget.chatId).notifier,
              );
              messageNotifier.clearReply();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMessagesList(List<Message> messages) {
    if (messages.isEmpty) {
      return _buildEmptyMessagesState();
    }

    // Assign a GlobalKey to each message for scrolling/highlighting if not already present
    for (final m in messages) {
      _messageKeys.putIfAbsent(
        m.id,
        () => GlobalKey<HighlightableMessageState>(),
      );
    }
    return ListView.builder(
      controller: _scrollController,
      reverse: true,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        final isMe = message.senderId == _currentUserId;
        final showSenderName = widget.isGroup && !isMe;
        final key = _messageKeys[message.id]!;
        return HighlightableMessage(
          key: key,
          isHighlighted: _highlightedMessageId == message.id,
          child: _buildSwipeableMessageWithMessages(
            message,
            isMe,
            showSenderName,
            messages,
          ),
        );
      },
    );
  }

  Widget _buildSwipeableMessageWithMessages(
    Message message,
    bool isMe,
    bool showSenderName,
    List<Message> messages,
  ) {
    return _SwipeToReply(
      message: message,
      isMe: isMe,
      onReply: () => _onReplyToMessage(message),
      onLongPress: () => _onMessageLongPress(message),
      child: _buildMessageBubbleWithMessages(
        message,
        isMe,
        showSenderName,
        messages,
      ),
    );
  }

  Widget _buildMessageBubbleWithMessages(
    Message message,
    bool isMe,
    bool showSenderName,
    List<Message> messages,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe && widget.isGroup) ...[
            CircleAvatar(
              radius: 12,
              backgroundColor: const Color(0xFF005368).withOpacity(0.1),
              child: const Icon(
                LucideIcons.user,
                size: 12,
                color: Color(0xFF005368),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isMe ? const Color(0xFF005368) : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isMe ? 16 : 4),
                  bottomRight: Radius.circular(isMe ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (showSenderName) ...[
                    Text(
                      message.senderName,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFFF2A738),
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (message.replyToMessageId != null) ...[
                    GestureDetector(
                      onTap: () {
                        final replyId = message.replyToMessageId;
                        if (replyId != null) {
                          _scrollToAndHighlightMessage(replyId);
                          setState(() {
                            _highlightedMessageId = replyId;
                          });
                          // Remove highlight after 3 seconds
                          Future.delayed(const Duration(seconds: 3), () {
                            if (mounted && _highlightedMessageId == replyId) {
                              setState(() {
                                _highlightedMessageId = null;
                              });
                            }
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        margin: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          color: (isMe ? Colors.white : const Color(0xFF005368))
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border(
                            left: BorderSide(
                              color:
                                  isMe ? Colors.white : const Color(0xFF005368),
                              width: 3,
                            ),
                          ),
                        ),
                        child: Text(
                          _getReplyToText(message, messages),
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color:
                                isMe
                                    ? Colors.white.withOpacity(0.8)
                                    : Colors.grey[600],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                  _buildMessageContent(message, isMe),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatMessageTime(message.timestamp),
                        style: GoogleFonts.poppins(
                          fontSize: 11,
                          color:
                              isMe
                                  ? Colors.white.withOpacity(0.8)
                                  : Colors.grey[500],
                        ),
                      ),
                      if (isMe) ...[
                        const SizedBox(width: 4),
                        Icon(
                          _getMessageStatusIcon(message.status),
                          size: 12,
                          color: _getMessageStatusIconColor(
                            message.status,
                            isMe,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getReplyToText(Message message, List<Message> messages) {
    if (message.replyToMessageId == null) return '';
    final replied = messages.firstWhere(
      (m) => m.id == message.replyToMessageId,
      orElse:
          () => Message(
            id: '',
            // chatId: '',
            senderId: '',
            senderName: '',
            timestamp: DateTime.now(),
            type: MessageType.text,
            status: MessageStatus.sent,
            text: message.replyToText ?? '',
          ),
    );
    // Prefer actual text, fallback to replyToText
    return replied.text?.isNotEmpty == true
        ? replied.text!
        : (message.replyToText ?? '');
  }

  Widget _buildEmptyMessagesState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.messageSquare,
            size: 64,
            color: Colors.grey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No messages yet',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start the conversation by sending a message',
            style: GoogleFonts.poppins(fontSize: 14, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    final isPermissionError = error.contains('permission-denied');

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isPermissionError ? LucideIcons.lock : LucideIcons.alertCircle,
              size: 64,
              color: Colors.red.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              isPermissionError
                  ? 'Authentication Error'
                  : 'Error loading messages',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isPermissionError
                  ? 'Please wait while we reconnect your session...'
                  : error,
              style: GoogleFonts.poppins(fontSize: 14, color: Colors.red[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () async {
                try {
                  // Force refresh the messages provider
                  ref.invalidate(messagesStreamProvider(widget.chatId));

                  // If it's a permission error, try to reinitialize Firebase Auth
                  if (isPermissionError) {
                    await ChatService.switchUser();
                    await Future.delayed(const Duration(milliseconds: 1000));
                    ref.invalidate(messagesStreamProvider(widget.chatId));
                  }
                } catch (e) {
                  if (mounted) {
                    AppSnackbar.showError(
                      context,
                      'Failed to retry: ${e.toString()}',
                    );
                  }
                }
              },
              icon: const Icon(LucideIcons.refreshCw, color: Colors.white),
              label: Text(
                'Retry',
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF005368),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(Message message, bool isMe, bool showSenderName) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe && widget.isGroup) ...[
            CircleAvatar(
              radius: 12,
              backgroundColor: const Color(0xFF005368).withOpacity(0.1),
              child: const Icon(
                LucideIcons.user,
                size: 12,
                color: Color(0xFF005368),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isMe ? const Color(0xFF005368) : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isMe ? 16 : 4),
                  bottomRight: Radius.circular(isMe ? 4 : 16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (showSenderName) ...[
                    Text(
                      message.senderName,
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFFF2A738),
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (message.replyToMessageId != null) ...[
                    GestureDetector(
                      onTap: () {
                        // Scroll to and highlight the replied message
                        // (Simple implementation: scroll to the message if found)
                        // You can improve this with animation/highlight if needed
                        // For now, just scroll to the top (newest) message
                        _scrollController.animateTo(
                          0,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        margin: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          color: (isMe ? Colors.white : const Color(0xFF005368))
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border(
                            left: BorderSide(
                              color:
                                  isMe ? Colors.white : const Color(0xFF005368),
                              width: 3,
                            ),
                          ),
                        ),
                        child: Builder(
                          builder: (context) {
                            // Fallback to replyToText if available
                            final replyText = message.replyToText ?? '';
                            return Text(
                              replyText.isNotEmpty
                                  ? replyText
                                  : 'Replied message',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color:
                                    isMe
                                        ? Colors.white.withOpacity(0.8)
                                        : Colors.grey[600],
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                  _buildMessageContent(message, isMe),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatMessageTime(message.timestamp),
                        style: GoogleFonts.poppins(
                          fontSize: 11,
                          color:
                              isMe
                                  ? Colors.white.withOpacity(0.8)
                                  : Colors.grey[500],
                        ),
                      ),
                      if (isMe) ...[
                        const SizedBox(width: 4),
                        Icon(
                          _getMessageStatusIcon(message.status),
                          size: 12,
                          color: _getMessageStatusIconColor(
                            message.status,
                            isMe,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageContent(Message message, bool isMe) {
    switch (message.type) {
      case MessageType.text:
        return Text(
          message.text ?? '',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: isMe ? Colors.white : Colors.black87,
          ),
        );
      case MessageType.image:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (message.mediaUrl != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: ImageUtils.buildChatImage(
                  message.mediaUrl!,
                  width: 200,
                  height: 200,
                  fit: BoxFit.cover,
                  borderRadius: BorderRadius.circular(8),
                  onTap: () {
                    showDialog(
                      context: context,
                      builder:
                          (_) => Dialog(
                            backgroundColor: Colors.transparent,
                            child: GestureDetector(
                              onTap: () => Navigator.of(context).pop(),
                              child: InteractiveViewer(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: ImageUtils.buildChatImage(
                                    message.mediaUrl!,
                                    width:
                                        MediaQuery.of(context).size.width * 0.9,
                                    height:
                                        MediaQuery.of(context).size.height *
                                        0.7,
                                    fit: BoxFit.contain,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ),
                    );
                  },
                ),
              ),
            if (message.text != null && message.text!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                message.text!,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: isMe ? Colors.white : Colors.black87,
                ),
              ),
            ],
          ],
        );
      case MessageType.file:
        return GestureDetector(
          onTap: () async {
            if (message.mediaUrl != null) {
              final url = Uri.parse(message.mediaUrl!);
              try {
                if (!await launchUrl(
                  url,
                  mode: LaunchMode.externalApplication,
                )) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Could not open file.')),
                    );
                  }
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Could not open file.')),
                  );
                }
              }
            }
          },
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: (isMe ? Colors.white : const Color(0xFF005368))
                  .withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  LucideIcons.file,
                  color: isMe ? Colors.white : const Color(0xFF005368),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        message.fileName ?? 'File',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: isMe ? Colors.white : Colors.black87,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (message.fileSize != null)
                        Text(
                          _formatFileSize(message.fileSize!),
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color:
                                isMe
                                    ? Colors.white.withOpacity(0.8)
                                    : Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      default:
        return Text(
          'Unsupported message type',
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontStyle: FontStyle.italic,
            color: isMe ? Colors.white.withOpacity(0.8) : Colors.grey[600],
          ),
        );
    }
  }

  String _formatMessageTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }

  IconData _getMessageStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return LucideIcons.clock;
      case MessageStatus.sent:
        return LucideIcons.check;
      case MessageStatus.delivered:
        return LucideIcons.checkCheck;
      case MessageStatus.read:
        return LucideIcons.checkCheck;
      case MessageStatus.failed:
        return LucideIcons.alertCircle;
    }
  }

  Color _getMessageStatusIconColor(MessageStatus status, bool isMe) {
    switch (status) {
      case MessageStatus.sending:
        return isMe ? Colors.white.withOpacity(0.8) : Colors.grey[500]!;
      case MessageStatus.sent:
        return isMe ? Colors.white.withOpacity(0.8) : Colors.grey[500]!;
      case MessageStatus.delivered:
        return isMe ? Colors.white.withOpacity(0.8) : Colors.grey[500]!;
      case MessageStatus.read:
        return Colors.lightBlue[400]!;
      case MessageStatus.failed:
        return Colors.redAccent;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  Widget _buildBroadcastIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF005368).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: const Color(0xFF005368).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(LucideIcons.radio, size: 16, color: const Color(0xFF005368)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Broadcasting to all group members',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: const Color(0xFF005368),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInputBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Broadcast indicator for group chats
          if (widget.isGroup) _buildBroadcastIndicator(),
          if (_selectedImages.isNotEmpty)
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  final img = _selectedImages[index];
                  return Stack(
                    children: [
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            File(img.path),
                            width: 70,
                            height: 70,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      // Loading overlay when sending
                      if (_isSendingImage)
                        Positioned.fill(
                          child: Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              color: Colors.black54,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      // Remove button (disabled when sending)
                      if (!_isSendingImage)
                        Positioned(
                          top: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _selectedImages.removeAt(index);
                              });
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.black54,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 18,
                              ),
                            ),
                          ),
                        ),
                    ],
                  );
                },
              ),
            ),
          Row(
            children: [
              // Attachment button
              IconButton(
                onPressed: _sendFileMessage,
                icon: const Icon(
                  LucideIcons.paperclip,
                  color: Color(0xFF005368),
                  size: 20,
                ),
              ),
              // Camera button
              IconButton(
                onPressed: _isSendingFromCamera ? null : _sendCameraImage,
                icon:
                    _isSendingFromCamera
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Color(0xFF005368),
                          ),
                        )
                        : const Icon(
                          LucideIcons.camera,
                          color: Color(0xFF005368),
                          size: 20,
                        ),
              ),
              // Text input
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _messageController,
                          decoration: InputDecoration(
                            hintText: 'Type a message...',
                            hintStyle: GoogleFonts.poppins(
                              color: Colors.grey[500],
                              fontSize: 14,
                            ),
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.zero,
                          ),
                          style: GoogleFonts.poppins(fontSize: 14),
                          maxLines: null,
                          textCapitalization: TextCapitalization.sentences,
                        ),
                      ),
                      IconButton(
                        onPressed:
                            _isSendingFromGallery
                                ? null
                                : _pickImagesFromGallery,
                        icon:
                            _isSendingFromGallery
                                ? const SizedBox(
                                  width: 18,
                                  height: 18,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Color(0xFF005368),
                                  ),
                                )
                                : const Icon(
                                  LucideIcons.image,
                                  color: Color(0xFF005368),
                                  size: 18,
                                ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // Send button
              GestureDetector(
                onTap:
                    _isSendingImage
                        ? null
                        : () {
                          if (_selectedImages.isNotEmpty) {
                            _sendSelectedImages();
                          } else {
                            _sendTextMessage();
                          }
                        },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        _isSendingImage
                            ? const Color(0xFF005368).withOpacity(0.6)
                            : const Color(0xFF005368),
                    shape: BoxShape.circle,
                  ),
                  child:
                      _isSendingImage
                          ? const SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                          : const Icon(
                            LucideIcons.send,
                            color: Colors.white,
                            size: 18,
                          ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showClearChatDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'Clear Chat',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Text(
              'Are you sure you want to clear all messages in this chat? This action cannot be undone.',
              style: GoogleFonts.poppins(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);

                  final chatNotifier = ref.read(chatProvider.notifier);
                  final success = await chatNotifier.clearChat(widget.chatId);

                  if (success && mounted) {
                    AppSnackbar.showSuccess(
                      context,
                      'Chat cleared successfully',
                    );
                  } else if (mounted) {
                    AppSnackbar.showError(context, 'Failed to clear chat');
                  }
                },
                child: Text(
                  'Clear',
                  style: GoogleFonts.poppins(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }

  void _showDeleteChatDialog() {
    final isGroup = widget.isGroup;
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              isGroup ? 'Delete Group' : 'Delete Chat',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            content: Text(
              isGroup
                  ? 'Are you sure you want to delete this group? This will remove the group for all members and cannot be undone.'
                  : 'Are you sure you want to delete this chat? This action cannot be undone.',
              style: GoogleFonts.poppins(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: GoogleFonts.poppins(color: Colors.grey[600]),
                ),
              ),
              TextButton(
                onPressed: () async {
                  // Store the navigator context before async operations
                  final navigator = Navigator.of(context);
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  navigator.pop(); // Close the dialog first

                  final chatNotifier = ref.read(chatProvider.notifier);
                  final success = await chatNotifier.deleteChat(widget.chatId);

                  if (success && mounted) {
                    // Navigate back to the previous screen (groups tab)
                    navigator.pop(); // Close the chat screen
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          isGroup
                              ? 'Group deleted successfully'
                              : 'Chat deleted successfully',
                          style: GoogleFonts.poppins(color: Colors.white),
                        ),
                        backgroundColor: Colors.green,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  } else if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          isGroup
                              ? 'Failed to delete group'
                              : 'Failed to delete chat',
                          style: GoogleFonts.poppins(color: Colors.white),
                        ),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                },
                child: Text(
                  isGroup ? 'Delete Group' : 'Delete',
                  style: GoogleFonts.poppins(color: Colors.red),
                ),
              ),
            ],
          ),
    );
  }
}

class _SwipeToReply extends StatefulWidget {
  final Message message;
  final bool isMe;
  final VoidCallback onReply;
  final VoidCallback onLongPress;
  final Widget child;

  const _SwipeToReply({
    required this.message,
    required this.isMe,
    required this.onReply,
    required this.onLongPress,
    required this.child,
  });

  @override
  State<_SwipeToReply> createState() => _SwipeToReplyState();
}

class _SwipeToReplyState extends State<_SwipeToReply>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;
  double _dragDistance = 0;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _slideAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _scaleAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onHorizontalDragUpdate(DragUpdateDetails details) {
    setState(() {
      _isDragging = true;
      // Both messages use right swipe (positive delta) for reply
      if (details.delta.dx > 0) {
        _dragDistance = (_dragDistance + details.delta.dx).clamp(0, 80);
      } else if (details.delta.dx < 0) {
        _dragDistance = (_dragDistance - details.delta.dx.abs()).clamp(0, 80);
      }
    });
  }

  void _onHorizontalDragEnd(DragEndDetails details) {
    // Check if we should trigger reply based on distance or velocity
    // Both messages use right swipe for reply
    bool shouldReply =
        _dragDistance > 40 ||
        (details.primaryVelocity != null && details.primaryVelocity! > 300);

    if (shouldReply) {
      _triggerReply();
    } else {
      // Animate back to original position
      _animateBack();
    }

    setState(() {
      _isDragging = false;
    });
  }

  void _triggerReply() {
    _animationController.forward().then((_) {
      widget.onReply();
      _animationController.reverse().then((_) {
        setState(() {
          _dragDistance = 0;
        });
      });
    });
  }

  void _animateBack() {
    // Smoothly animate back to original position
    final controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    final animation = Tween<double>(
      begin: _dragDistance,
      end: 0,
    ).animate(CurvedAnimation(parent: controller, curve: Curves.easeOut));

    animation.addListener(() {
      setState(() {
        _dragDistance = animation.value;
      });
    });

    controller.forward().then((_) {
      controller.dispose();
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: widget.onLongPress,
      onHorizontalDragUpdate: _onHorizontalDragUpdate,
      onHorizontalDragEnd: _onHorizontalDragEnd,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Stack(
            children: [
              // Reply icon background
              if (_isDragging || _animationController.isAnimating)
                Positioned(
                  left: 20, // Always show on left side for right swipe
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: AnimatedBuilder(
                      animation: _scaleAnimation,
                      builder: (context, child) {
                        final progress =
                            _isDragging
                                ? (_dragDistance / 80).clamp(0.0, 1.0)
                                : _scaleAnimation.value;

                        return Transform.scale(
                          scale:
                              0.6 + (progress * 0.4), // Scale from 0.6 to 1.0
                          child: Opacity(
                            opacity: progress,
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: const Color(0xFF005368),
                                borderRadius: BorderRadius.circular(20),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(
                                      0xFF005368,
                                    ).withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.reply,
                                color: Colors.white,
                                size: 22,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              // Message content
              Transform.translate(
                offset: Offset(
                  _isDragging
                      ? _dragDistance *
                          0.8 // Both messages move right during swipe
                      : _slideAnimation.value *
                          40, // Both messages move right during animation
                  0,
                ),
                child: AnimatedContainer(
                  duration:
                      _isDragging
                          ? Duration.zero
                          : const Duration(milliseconds: 200),
                  curve: Curves.easeOut,
                  child: widget.child,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
