import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/providers/distributor_provider.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_details_tab.dart';
import 'package:mr_garments_mobile/widgets/invoice_tab.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class DistributorDetailsScreen extends ConsumerStatefulWidget {
  final int distributorId;
  const DistributorDetailsScreen({super.key, required this.distributorId});

  @override
  ConsumerState<DistributorDetailsScreen> createState() =>
      _DistributorDetailsScreenState();
}

class _DistributorDetailsScreenState
    extends ConsumerState<DistributorDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Fetch distributor details
    Future.microtask(() {
      ref
          .read(distributorsProvider.notifier)
          .fetchDistributorDetails(widget.distributorId);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF005368),
      elevation: 0,
      foregroundColor: Colors.white,
      title: Row(
        children: [
          Text(
            "Distributor Details",
            style: GoogleFonts.poppins(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () {
              Navigator.pushAndRemoveUntil(
                context,
                MaterialPageRoute(builder: (context) => AdminHomePage()),
                (route) => false,
              );
            },
            child: const Icon(Icons.home, color: Colors.white),
          ),
        ],
      ),
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        tabs: [
          Tab(child: Text("Details", style: GoogleFonts.poppins())),
          Tab(child: Text("Invoice", style: GoogleFonts.poppins())),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(12),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                //Implement chat logic
              },
              icon: const Icon(Icons.chat_bubble_outline, color: Colors.white),
              label: Text(
                "Chat",
                style: GoogleFonts.poppins(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF005368),
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () async {
                // final confirm = await showDialog<bool>(
                //   context: context,
                //   builder:
                //       (ctx) => AlertDialog(
                //         title: const Text("Confirm Deactivation"),
                //         content: const Text(
                //           "Are you sure you want to deactivate this distributor?",
                //         ),
                //         actions: [
                //           TextButton(
                //             onPressed: () => Navigator.pop(ctx, false),
                //             child: const Text("Cancel"),
                //           ),
                //           ElevatedButton(
                //             onPressed: () => Navigator.pop(ctx, true),
                //             child: const Text("Deactivate"),
                //           ),
                //         ],
                //       ),
                // );

                // if (confirm == true) {
                //   try {
                await ref
                    .read(distributorsProvider.notifier)
                    .updateDistributorStatus(
                      widget.distributorId,
                      "deactivated",
                    );
                if (!mounted) return;
                AppSnackbar.showSuccess(context, "Distributor deactivated");

                // Optionally go back or refresh
                Navigator.pop(context);
                // } catch (e) {
                //   ScaffoldMessenger.of(context).showSnackBar(
                //     SnackBar(content: Text("Failed to deactivate: $e")),
                //   );
                // }
                // }
              },
              icon: const Icon(Icons.cancel_outlined, color: Colors.white),
              label: Text(
                "Deactivate",
                style: GoogleFonts.poppins(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                DistributorDetailsTab(distributorId: widget.distributorId),
                InvoiceTab(),
              ],
            ),
          ),
          _buildBottomButtons(),
        ],
      ),
    );
  }
}
