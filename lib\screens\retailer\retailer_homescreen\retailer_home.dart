import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_homescreen/retailer_sidebar.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/screens/widgets/brands_section.dart';
import 'package:mr_garments_mobile/screens/widgets/brands_viewall.dart';
import 'package:mr_garments_mobile/screens/widgets/catalog_section.dart';
import 'package:mr_garments_mobile/screens/widgets/catalog_viewall.dart';
import 'package:mr_garments_mobile/screens/widgets/categories_section.dart';
import 'package:mr_garments_mobile/screens/widgets/categories_viewall.dart';
import 'package:mr_garments_mobile/screens/widgets/custom_bottom_nav_bar.dart';

class RetailerHomeScreen extends StatefulWidget {
  const RetailerHomeScreen({super.key});

  @override
  State<RetailerHomeScreen> createState() => _RetailerHomeScreenState();
}

class _RetailerHomeScreenState extends State<RetailerHomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final int _selectedIndex = 0;
  bool _showWelcomeCard = true;
  String _retailerName = 'Retailer';

  final List<String> brandImages = [
    'assets/images/brands/brand1.jpg',
    'assets/images/brands/brand2.jpg',
    'assets/images/brands/brand3.jpg',
    'assets/images/brands/brand4.jpg',
    'assets/images/brands/brand5.jpg',
    'assets/images/brands/brand6.jpg',
    'assets/images/brands/brand7.jpg',
  ];

  final List<Map<String, String>> categoryData = [
    {'image': 'assets/images/categories/category1.jpg', 'title': 'Tops'},
    {'image': 'assets/images/categories/category2.jpg', 'title': 'T-shirts'},
    {'image': 'assets/images/categories/category3.jpg', 'title': 'Shirts'},
    {'image': 'assets/images/categories/category4.jpg', 'title': 'Denim'},
    {'image': 'assets/images/categories/category5.jpg', 'title': 'Shorts'},
    {'image': 'assets/images/categories/category6.jpg', 'title': 'Kurtis'},
    {'image': 'assets/images/categories/category7.jpg', 'title': 'Top'},
  ];

  final List<Map<String, String>> catalogList = [
    {
      'image': 'assets/images/catalogs/cat1.jpg',
      'brand': 'Brand One',
      'catalog': 'CAT-101',
    },
    {
      'image': 'assets/images/catalogs/cat2.jpg',
      'brand': 'Brand Two',
      'catalog': 'CAT-102',
    },
    {
      'image': 'assets/images/catalogs/cat3.jpg',
      'brand': 'Brand Three',
      'catalog': 'CAT-103',
    },
    {
      'image': 'assets/images/catalogs/cat4.jpg',
      'brand': 'Brand Four',
      'catalog': 'CAT-104',
    },
    {
      'image': 'assets/images/catalogs/cat5.jpg',
      'brand': 'Brand Five',
      'catalog': 'CAT-105',
    },
    {
      'image': 'assets/images/catalogs/cat6.jpg',
      'brand': 'Brand Six',
      'catalog': 'CAT-106',
    },
    {
      'image': 'assets/images/catalogs/cat7.jpg',
      'brand': 'Brand Seven',
      'catalog': 'CAT-107',
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadRetailerName();
    Future.delayed(const Duration(seconds: 10), () {
      setState(() => _showWelcomeCard = false);
    });
  }

  void _loadRetailerName() async {
    try {
      final userName = await SessionService.getUserName();
      if (userName != null && userName.isNotEmpty) {
        setState(() {
          _retailerName = userName;
        });
      }
    } catch (e) {
      // Keep default name if there's an error
    }
  }

  void _navigateTo(String section) {
    if (section == 'Brands') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const BrandsViewallScreen()),
      );
    } else if (section == 'Categories') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const CategoriesViewall()),
      );
    } else if (section == 'Catalog') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const CatalogViewall()),
      );
      // } else if (section == 'Product') {
      //   Navigator.push(context, MaterialPageRoute(builder: (_) => const ProductsScreen()));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      drawer: RetailerSidebar(),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(90),
        child: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: const Color(0xFF00536B),
          elevation: 4,
          flexibleSpace: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => _scaffoldKey.currentState?.openDrawer(),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(8),
                      child: const Icon(Icons.menu, color: Color(0xFF00536B)),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Search...',
                        hintStyle: GoogleFonts.poppins(color: Colors.white70),
                        prefixIcon: const Icon(
                          Icons.search,
                          color: Colors.white,
                        ),
                        filled: true,
                        fillColor: Colors.white.withAlpha(26),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 0),
                      ),
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 10),
                  const Icon(
                    Icons.notifications_none,
                    color: Colors.white,
                    size: 25,
                  ),
                  const SizedBox(width: 10),
                  // const CircleAvatar(
                  //   backgroundImage: AssetImage('assets/profile.jpg'),
                  //   radius: 18,
                  // ),
                ],
              ),
            ),
          ),
        ),
      ),

      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(14),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AnimatedOpacity(
                opacity: _showWelcomeCard ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 600),
                child:
                    _showWelcomeCard
                        ? Container(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: const Color.fromARGB(255, 238, 236, 236),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(13),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                            border: Border.all(
                              color: Colors.white.withAlpha(55),
                            ),
                          ),
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Hi, $_retailerName',
                                style: GoogleFonts.poppins(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFFF2A738),
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Welcome to Retailer Panel!',
                                style: GoogleFonts.poppins(
                                  fontSize: 22,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF005368),
                                ),
                              ),
                            ],
                          ),
                        )
                        : const SizedBox.shrink(),
              ),
              const SizedBox(height: 20),
              BrandsSection(
                brandImages: brandImages,
                onViewAll: () => _navigateTo('Brands'),
              ),
              const SizedBox(height: 30),
              CategoriesSection(
                categories: categoryData,
                onViewAll: () => _navigateTo('Categories'),
              ),
              const SizedBox(height: 35),
              CatalogSection(
                catalogList: catalogList,
                onViewAll: () => _navigateTo('Catalog'),
              ),
            ],
          ),
        ),
      ),

      bottomNavigationBar: CustomBottomNavBar(currentIndex: _selectedIndex),
    );
  }
}
