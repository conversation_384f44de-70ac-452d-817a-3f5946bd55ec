// lib/services/auth_service.dart
import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/services/chat_service.dart';
import 'package:mr_garments_mobile/services/credential_service.dart';

class AuthService {
  static const String baseUrl = 'https://mrgarment.braincavesoft.com/api';
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Initialize Firebase Auth for chat features
  static Future<void> _initializeFirebaseAuth() async {
    try {
      // Sign in anonymously if not already authenticated
      if (_auth.currentUser == null) {
        await _auth.signInAnonymously();
      }
    } catch (e) {
      // Firebase auth initialization failed, but don't block login
    }
  }

  static Future<Map<String, dynamic>> register({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String accountType,
    required bool agreeTerms,
  }) async {
    final url = Uri.parse('$baseUrl/register');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'name': name,
        'email': email,
        'mobile_number': mobile,
        'password': password,
        'account_type': accountType,
        'agree_terms': agreeTerms,
      }),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          "success": true,
          "message": responseData['message'],
          "token": responseData['token'],
          "user": responseData['user'],
        };
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Registration failed',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'The email has already been taken.'};
    }
  }

  static Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    final url = Uri.parse('$baseUrl/login');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'password': password}),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {
          "success": true,
          "message": responseData['message'],
          "token": responseData['token'],
          "user": responseData['user'],
        };
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        return {"success": false, "message": "Invalid email or password."};
      } else {
        return {
          "success": false,
          "message":
              responseData['message'] ?? 'Login failed. Please try again.',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'Invalid email or password.'};
    }
  }

  static Future<Map<String, dynamic>> forgotPasswordRequest(
    String email,
  ) async {
    final url = Uri.parse('$baseUrl/forgot-password/request');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email}),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {"success": true, "message": responseData['message']};
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Request failed',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'Unexpected server response.'};
    }
  }

  static Future<Map<String, dynamic>> verifyOtp(
    String email,
    String otp,
  ) async {
    final url = Uri.parse('$baseUrl/forgot-password/verify');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({'email': email, 'otp': otp}),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {"success": true, "message": responseData['message']};
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Verification failed',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'Unexpected server response.'};
    }
  }

  static Future<Map<String, dynamic>> resetPassword({
    required String email,
    required String otp,
    required String password,
    required String passwordConfirmation,
  }) async {
    final url = Uri.parse('$baseUrl/forgot-password/reset');

    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': email,
        'otp': otp,
        'password': password,
        'password_confirmation': passwordConfirmation,
      }),
    );

    try {
      final responseData = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return {"success": true, "message": responseData['message']};
      } else {
        return {
          "success": false,
          "message": responseData['message'] ?? 'Reset password failed',
        };
      }
    } catch (e) {
      return {"success": false, "message": 'Unexpected server response.'};
    }
  }

  static Future<void> logout(String token) async {
    final url = Uri.parse('$baseUrl/logout');

    final response = await http.post(
      url,
      headers: {'Authorization': 'Bearer $token', 'Accept': 'application/json'},
    );
    if (response.statusCode == 200) {
      // Success - logout API responded with 200
      // Clear local session data
      await SessionService.clearSession();
    } else {
      throw Exception('Failed to logout. Status: ${response.statusCode}');
    }
  }

  /// Enhanced login method that saves session data
  static Future<Map<String, dynamic>> loginWithSession({
    required String email,
    required String password,
  }) async {
    final result = await login(email: email, password: password);

    if (result['success'] == true &&
        result['token'] != null &&
        result['user'] != null) {
      // Save session data
      await SessionService.saveUserSession(
        token: result['token'],
        userData: result['user'],
      );

      // Initialize Firebase Auth for chat features
      await _initializeFirebaseAuth();

      // Sync user with Firebase for chat functionality
      await ChatService.syncCurrentUserWithFirebase();
    }

    return result;
  }

  /// Enhanced register method that saves session data
  static Future<Map<String, dynamic>> registerWithSession({
    required String name,
    required String email,
    required String mobile,
    required String password,
    required String accountType,
    required bool agreeTerms,
  }) async {
    final result = await register(
      name: name,
      email: email,
      mobile: mobile,
      password: password,
      accountType: accountType,
      agreeTerms: agreeTerms,
    );

    if (result['success'] == true &&
        result['token'] != null &&
        result['user'] != null) {
      // Save session data
      await SessionService.saveUserSession(
        token: result['token'],
        userData: result['user'],
      );

      // Initialize Firebase Auth for chat features
      await _initializeFirebaseAuth();

      // Sync user with Firebase for chat functionality
      await ChatService.syncCurrentUserWithFirebase();
    }

    return result;
  }

  /// Logout and clear session
  static Future<void> logoutWithSession() async {
    final token = await SessionService.getAuthToken();
    if (token != null) {
      try {
        await logout(token);
      } catch (e) {
        // Even if API call fails, clear local session
        await SessionService.clearSession();
        // Also clear saved credentials on logout
        await CredentialService.clearCredentials();
        rethrow;
      }
    } else {
      // No token found, just clear local session
      await SessionService.clearSession();
      // Also clear saved credentials on logout
      await CredentialService.clearCredentials();
    }
  }
}
