import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/screens/chat/chat_list_page.dart';
import 'package:mr_garments_mobile/screens/chat/groups_page.dart';
import 'package:mr_garments_mobile/screens/chat/profile_page.dart';
import 'package:mr_garments_mobile/screens/chat/more_page.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/chat_search_delegate.dart';
import 'package:mr_garments_mobile/screens/chat/widgets/add_chat_bottom_sheet.dart';

class ChatMainPage extends ConsumerStatefulWidget {
  const ChatMainPage({super.key});

  @override
  ConsumerState<ChatMainPage> createState() => _ChatMainPageState();
}

class _ChatMainPageState extends ConsumerState<ChatMainPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _navigateToHome() {
    // Navigate back to the appropriate home screen based on user role
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  void _showSearch() {
    showSearch(context: context, delegate: ChatSearchDelegate());
  }

  void _showAddChatBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddChatBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(120),
        child: AppBar(
          backgroundColor: const Color(0xFF005368),
          foregroundColor: Colors.white,
          elevation: 0,
          // automaticallyImplyLeading: false,
          flexibleSpace: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              child: Column(
                children: [
                  // Top row with back arrow, title, and home icon
                  Row(
                    children: [
                    //   GestureDetector(
                    //     onTap: () => Navigator.of(context).pop(),
                    //     child: Container(
                    //       decoration: const BoxDecoration(
                    //         color: Colors.white,
                    //         shape: BoxShape.circle,
                    //       ),
                    //       padding: const EdgeInsets.all(8),
                    //       child: const Icon(
                    //         Icons.arrow_back,
                    //         color: Color(0xFF005368),
                    //         size: 20,
                    //       ),
                    //     ),
                    //   ),
                      // Expanded(
                      //   child: Center(
                      //     child: Text(
                      //       'Chat',
                      //       style: GoogleFonts.poppins(
                      //         fontSize: 20,
                      //         fontWeight: FontWeight.w600,
                      //         color: Colors.white,
                      //       ),
                      //     ),
                      //   ),
                      // ),
                      Spacer(),
                      GestureDetector(
                        onTap: _navigateToHome,
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.home,
                            color: Color(0xFF005368),
                            size: 20,
                          ),
                        ),
                      ),
                       const SizedBox(width: 7),
                      GestureDetector(
                        onTap: _showAddChatBottomSheet,
                        child: Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF2A738),
                            shape: BoxShape.circle,
                          ),
                          padding: const EdgeInsets.all(8),
                          child: const Icon(
                            Icons.add,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Search bar and plus button
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: _showSearch,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),

                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color: Colors.white.withOpacity(0.3),
                              ),
                            ),
                            child: Row( 
                              children: [
                                Icon(
                                  LucideIcons.search,
                                  color: Colors.white.withOpacity(0.8),
                                  size: 18,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Search chats...',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white.withOpacity(0.8),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),

      body: TabBarView(
        controller: _tabController,
        children: const [
          ChatListPage(),
          GroupsPage(),
          ProfilePage(),
          MorePage(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: TabBar(
          controller: _tabController,
          indicatorColor: Colors.transparent,
          labelStyle: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
          unselectedLabelStyle: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w400,
          ),
          tabs: [
            Tab(
              icon: Icon(
                _currentIndex == 0
                    ? LucideIcons.messageSquare
                    : LucideIcons.messageSquare,
                size: 20,
              ),
              text: 'Chat',
            ),
            Tab(
              icon: Icon(
                _currentIndex == 1 ? LucideIcons.users : LucideIcons.users,
                size: 20,
              ),
              text: 'Groups',
            ),
            Tab(
              icon: Icon(
                _currentIndex == 2 ? LucideIcons.user : LucideIcons.user,
                size: 20,
              ),
              text: 'Profile',
            ),
            Tab(
              icon: Icon(
                _currentIndex == 3
                    ? LucideIcons.moreHorizontal
                    : LucideIcons.moreHorizontal,
                size: 20,
              ),
              text: 'More',
            ),
          ],
        ),
      ),
    );
  }
}
