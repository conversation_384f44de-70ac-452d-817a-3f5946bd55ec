import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:mr_garments_mobile/screens/manufacturer/Manufacturer_homescreen/manufacturer_home.dart';
import 'package:mr_garments_mobile/screens/retailer/retailer_homescreen/retailer_home.dart';
import 'package:mr_garments_mobile/screens/distributor/distributor_homescreen/distributor_home.dart';
import 'package:mr_garments_mobile/screens/admin/admin_home_page.dart';
import 'package:mr_garments_mobile/screens/widgets/products_tab.dart';
import 'package:mr_garments_mobile/screens/chat/chat_main_page.dart';
import 'package:mr_garments_mobile/services/session_service.dart';
import 'package:mr_garments_mobile/utils/snackbar.dart';

class CustomBottomNavBar extends StatefulWidget {
  final int currentIndex;

  const CustomBottomNavBar({super.key, required this.currentIndex});

  @override
  State<CustomBottomNavBar> createState() => _CustomBottomNavBarState();
}

class _CustomBottomNavBarState extends State<CustomBottomNavBar> {
  void _handleNavigation(BuildContext context, int index) async {
    if (index == 0) {
      // Home → Navigate to appropriate home screen based on user role
      await _navigateToHome(context);
    } else if (index == 2) {
      // Products → Replace stack with ProductsTab
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (_) => const ProductsTab()),
        (route) => false,
      );
    } else if (index == 4) {
      // Chat → Navigate to Chat Main Page
      Navigator.push(
        context,
        MaterialPageRoute(builder: (_) => const ChatMainPage()),
      );
    } else {
      // Other tabs → Show a placeholder message or implement later
      AppSnackbar.showInfo(context, 'This feature is under development!');
    }
  }

  Future<void> _navigateToHome(BuildContext context) async {
    try {
      final userRole = await SessionService.getUserRole();
      Widget homeScreen;

      switch (userRole?.toLowerCase()) {
        case 'manufacturer':
          homeScreen = const ManufacturerHomeScreen();
          break;
        case 'retailer':
          homeScreen = const RetailerHomeScreen();
          break;
        case 'distributor':
          homeScreen = const DistributorHomeScreen();
          break;
        case 'admin':
          homeScreen = const AdminHomePage();
          break;
        case 'staff':
          // Staff members navigate to their company's home screen
          // We need to get the company type from user data
          final userData = await SessionService.getUserData();
          final companyType =
              userData?['company_type']?.toString().toLowerCase();
          switch (companyType) {
            case 'manufacturer':
              homeScreen = const ManufacturerHomeScreen();
              break;
            case 'retailer':
              homeScreen = const RetailerHomeScreen();
              break;
            case 'distributor':
              homeScreen = const DistributorHomeScreen();
              break;
            default:
              // Default to manufacturer if company type is unknown
              homeScreen = const ManufacturerHomeScreen();
          }
          break;
        default:
          // If role is unknown, default to manufacturer
          homeScreen = const ManufacturerHomeScreen();
      }

      if (mounted && context.mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (_) => homeScreen),
          (route) => false,
        );
      }
    } catch (e) {
      // If there's an error, default to manufacturer home
      if (mounted && context.mounted) {
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (_) => const ManufacturerHomeScreen()),
          (route) => false,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      currentIndex: widget.currentIndex,
      selectedItemColor: const Color(0xFF00536B),
      unselectedItemColor: Colors.grey,
      onTap: (index) => _handleNavigation(context, index),
      showUnselectedLabels: true,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
        BottomNavigationBarItem(icon: Icon(LucideIcons.edit), label: 'Order'),
        BottomNavigationBarItem(
          icon: Icon(LucideIcons.package),
          label: 'Product',
        ),
        BottomNavigationBarItem(
          icon: Icon(LucideIcons.dollarSign),
          label: 'Ledger',
        ),
        BottomNavigationBarItem(
          icon: Icon(LucideIcons.messageSquare),
          label: 'Chat',
        ),
      ],
    );
  }
}
